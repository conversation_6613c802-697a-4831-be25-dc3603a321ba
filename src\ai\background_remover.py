"""
AI Background Remover - Intelligent background removal using AI models
Provides multiple methods for background removal including rembg and custom models.
"""

import logging
import numpy as np
from PIL import Image
from typing import Optional, Tuple
import io

try:
    import rembg
    REMBG_AVAILABLE = True
except ImportError:
    REMBG_AVAILABLE = False
    logging.warning("rembg not available. Background removal will use fallback methods.")

try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    logging.warning("OpenCV not available. Some background removal features will be limited.")

class BackgroundRemover:
    """AI-powered background removal with multiple algorithms."""
    
    def __init__(self):
        """Initialize the background remover."""
        self.logger = logging.getLogger(__name__)
        self.rembg_session = None
        
        if REMBG_AVAILABLE:
            try:
                # Initialize rembg with the default model
                self.rembg_session = rembg.new_session('u2net')
                self.logger.info("Initialized rembg with u2net model")
            except Exception as e:
                self.logger.error(f"Failed to initialize rembg: {e}")
                self.rembg_session = None
    
    def remove_background_rembg(self, image: Image.Image, model: str = 'u2net') -> Optional[Image.Image]:
        """
        Remove background using rembg library.
        
        Args:
            image: Input PIL Image
            model: Model to use ('u2net', 'u2net_human_seg', 'silueta', etc.)
            
        Returns:
            Image with background removed or None if failed
        """
        if not REMBG_AVAILABLE:
            self.logger.error("rembg library not available")
            return None
        
        try:
            # Convert PIL image to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
            
            # Create session if needed or if model changed
            if not self.rembg_session or model != 'u2net':
                self.rembg_session = rembg.new_session(model)
            
            # Remove background
            output_bytes = rembg.remove(img_byte_arr, session=self.rembg_session)
            
            # Convert back to PIL Image
            output_image = Image.open(io.BytesIO(output_bytes))
            
            self.logger.info(f"Background removed using rembg model: {model}")
            return output_image
            
        except Exception as e:
            self.logger.error(f"Failed to remove background with rembg: {e}")
            return None
    
    def remove_background_grabcut(self, image: Image.Image, iterations: int = 5) -> Optional[Image.Image]:
        """
        Remove background using OpenCV's GrabCut algorithm.
        
        Args:
            image: Input PIL Image
            iterations: Number of GrabCut iterations
            
        Returns:
            Image with background removed or None if failed
        """
        if not OPENCV_AVAILABLE:
            self.logger.error("OpenCV not available")
            return None
        
        try:
            # Convert PIL to OpenCV format
            img_array = np.array(image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            height, width = img_cv.shape[:2]
            
            # Create mask
            mask = np.zeros((height, width), np.uint8)
            
            # Define rectangle around the object (assuming object is in center)
            rect = (int(width * 0.1), int(height * 0.1), 
                   int(width * 0.8), int(height * 0.8))
            
            # Initialize background and foreground models
            bgd_model = np.zeros((1, 65), np.float64)
            fgd_model = np.zeros((1, 65), np.float64)
            
            # Apply GrabCut
            cv2.grabCut(img_cv, mask, rect, bgd_model, fgd_model, iterations, cv2.GC_INIT_WITH_RECT)
            
            # Create final mask
            mask2 = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
            
            # Apply mask to create RGBA image
            img_rgba = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGBA)
            img_rgba[:, :, 3] = mask2 * 255
            
            # Convert back to PIL
            result_image = Image.fromarray(img_rgba, 'RGBA')
            
            self.logger.info("Background removed using GrabCut algorithm")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Failed to remove background with GrabCut: {e}")
            return None
    
    def remove_background_color_threshold(self, image: Image.Image, 
                                        target_color: Tuple[int, int, int] = (255, 255, 255),
                                        threshold: int = 30) -> Optional[Image.Image]:
        """
        Remove background based on color similarity.
        
        Args:
            image: Input PIL Image
            target_color: RGB color to remove
            threshold: Color similarity threshold (0-255)
            
        Returns:
            Image with background removed or None if failed
        """
        try:
            # Convert to RGBA if not already
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # Convert to numpy array
            img_array = np.array(image)
            
            # Calculate color distance
            r, g, b = target_color
            color_diff = np.sqrt(
                (img_array[:, :, 0] - r) ** 2 +
                (img_array[:, :, 1] - g) ** 2 +
                (img_array[:, :, 2] - b) ** 2
            )
            
            # Create mask based on threshold
            mask = color_diff > threshold
            
            # Apply mask to alpha channel
            img_array[:, :, 3] = mask * 255
            
            # Convert back to PIL
            result_image = Image.fromarray(img_array, 'RGBA')
            
            self.logger.info(f"Background removed using color threshold: {target_color}")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Failed to remove background with color threshold: {e}")
            return None
    
    def remove_background_edge_detection(self, image: Image.Image) -> Optional[Image.Image]:
        """
        Remove background using edge detection and morphological operations.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Image with background removed or None if failed
        """
        if not OPENCV_AVAILABLE:
            self.logger.error("OpenCV not available")
            return None
        
        try:
            # Convert to grayscale for edge detection
            gray = image.convert('L')
            gray_array = np.array(gray)
            
            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray_array, (5, 5), 0)
            
            # Edge detection using Canny
            edges = cv2.Canny(blurred, 50, 150)
            
            # Morphological operations to close gaps
            kernel = np.ones((3, 3), np.uint8)
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
            edges = cv2.dilate(edges, kernel, iterations=2)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Create mask from largest contour
            mask = np.zeros(gray_array.shape, np.uint8)
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                cv2.fillPoly(mask, [largest_contour], 255)
            
            # Apply mask to create RGBA image
            img_array = np.array(image.convert('RGBA'))
            img_array[:, :, 3] = mask
            
            result_image = Image.fromarray(img_array, 'RGBA')
            
            self.logger.info("Background removed using edge detection")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Failed to remove background with edge detection: {e}")
            return None
    
    def auto_remove_background(self, image: Image.Image) -> Optional[Image.Image]:
        """
        Automatically choose the best background removal method.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Image with background removed or None if failed
        """
        # Try rembg first (most accurate)
        if REMBG_AVAILABLE and self.rembg_session:
            result = self.remove_background_rembg(image)
            if result:
                return result
        
        # Try GrabCut as fallback
        if OPENCV_AVAILABLE:
            result = self.remove_background_grabcut(image)
            if result:
                return result
        
        # Try color threshold for white backgrounds
        result = self.remove_background_color_threshold(image)
        if result:
            return result
        
        self.logger.warning("All background removal methods failed")
        return None
    
    def refine_edges(self, image: Image.Image, feather_radius: int = 2) -> Optional[Image.Image]:
        """
        Refine edges of a transparent image.
        
        Args:
            image: Input PIL Image with transparency
            feather_radius: Radius for edge feathering
            
        Returns:
            Image with refined edges or None if failed
        """
        if not OPENCV_AVAILABLE:
            return image
        
        try:
            if image.mode != 'RGBA':
                return image
            
            img_array = np.array(image)
            alpha = img_array[:, :, 3]
            
            # Apply Gaussian blur to alpha channel for feathering
            if feather_radius > 0:
                alpha_blurred = cv2.GaussianBlur(alpha, (feather_radius * 2 + 1, feather_radius * 2 + 1), 0)
                img_array[:, :, 3] = alpha_blurred
            
            result_image = Image.fromarray(img_array, 'RGBA')
            
            self.logger.info("Edges refined successfully")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Failed to refine edges: {e}")
            return image
    
    def get_available_models(self) -> list:
        """
        Get list of available background removal models.
        
        Returns:
            List of available model names
        """
        models = ['color_threshold', 'edge_detection']
        
        if OPENCV_AVAILABLE:
            models.append('grabcut')
        
        if REMBG_AVAILABLE:
            models.extend(['u2net', 'u2net_human_seg', 'silueta', 'isnet-general-use'])
        
        return models
