"""
GIF and Animation Manager - Handle animated images, GIF creation, and frame management
Provides comprehensive animation support including frame editing and timeline management.
"""

import logging
import os
from PIL import Image, ImageSequence
from typing import List, Optional, Tuple, Dict
import imageio
import numpy as np

try:
    from moviepy.editor import VideoFileClip, ImageSequenceClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    logging.warning("MoviePy not available. Video conversion features will be limited.")

class GIFManager:
    """Manages animated GIFs and frame-by-frame editing."""
    
    def __init__(self):
        """Initialize the GIF manager."""
        self.logger = logging.getLogger(__name__)
        self.frames: List[Image.Image] = []
        self.frame_durations: List[int] = []
        self.current_frame_index: int = 0
        self.loop_count: int = 0
        self.is_animated: bool = False
        
    def load_animated_image(self, file_path: str) -> bool:
        """
        Load an animated image (GIF, WebP, etc.).
        
        Args:
            file_path: Path to the animated image file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            image = Image.open(file_path)
            
            # Check if image is animated
            if hasattr(image, 'is_animated') and image.is_animated:
                self.is_animated = True
                self.frames = []
                self.frame_durations = []
                
                # Extract all frames
                for frame in ImageSequence.Iterator(image):
                    # Convert frame to RGB if necessary
                    if frame.mode != 'RGB':
                        frame = frame.convert('RGB')
                    
                    self.frames.append(frame.copy())
                    
                    # Get frame duration (in milliseconds)
                    duration = frame.info.get('duration', 100)
                    self.frame_durations.append(duration)
                
                # Get loop count
                self.loop_count = image.info.get('loop', 0)
                self.current_frame_index = 0
                
                self.logger.info(f"Loaded animated image: {len(self.frames)} frames, {file_path}")
                return True
            else:
                # Single frame image
                self.is_animated = False
                self.frames = [image.convert('RGB')]
                self.frame_durations = [100]
                self.current_frame_index = 0
                
                self.logger.info(f"Loaded static image: {file_path}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to load animated image {file_path}: {e}")
            return False
    
    def save_as_gif(self, file_path: str, duration: Optional[List[int]] = None, 
                   loop: int = 0, optimize: bool = True) -> bool:
        """
        Save frames as an animated GIF.
        
        Args:
            file_path: Output file path
            duration: Frame durations in milliseconds (uses current if None)
            loop: Loop count (0 = infinite)
            optimize: Whether to optimize the GIF
            
        Returns:
            True if successful, False otherwise
        """
        if not self.frames:
            self.logger.warning("No frames to save")
            return False
        
        try:
            durations = duration or self.frame_durations
            if len(durations) != len(self.frames):
                durations = [100] * len(self.frames)
            
            # Save as GIF
            self.frames[0].save(
                file_path,
                save_all=True,
                append_images=self.frames[1:],
                duration=durations,
                loop=loop,
                optimize=optimize
            )
            
            self.logger.info(f"Saved GIF: {file_path}, {len(self.frames)} frames")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save GIF {file_path}: {e}")
            return False
    
    def add_frame(self, image: Image.Image, duration: int = 100, index: Optional[int] = None) -> bool:
        """
        Add a frame to the animation.
        
        Args:
            image: PIL Image to add
            duration: Frame duration in milliseconds
            index: Position to insert frame (appends if None)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            if index is None:
                self.frames.append(image.copy())
                self.frame_durations.append(duration)
            else:
                self.frames.insert(index, image.copy())
                self.frame_durations.insert(index, duration)
            
            self.is_animated = len(self.frames) > 1
            self.logger.info(f"Added frame at index {index or len(self.frames)-1}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add frame: {e}")
            return False
    
    def remove_frame(self, index: int) -> bool:
        """
        Remove a frame from the animation.
        
        Args:
            index: Index of frame to remove
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if 0 <= index < len(self.frames):
                self.frames.pop(index)
                self.frame_durations.pop(index)
                
                # Adjust current frame index
                if self.current_frame_index >= len(self.frames):
                    self.current_frame_index = max(0, len(self.frames) - 1)
                
                self.is_animated = len(self.frames) > 1
                self.logger.info(f"Removed frame at index {index}")
                return True
            else:
                self.logger.warning(f"Invalid frame index: {index}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to remove frame: {e}")
            return False
    
    def duplicate_frame(self, index: int) -> bool:
        """
        Duplicate a frame in the animation.
        
        Args:
            index: Index of frame to duplicate
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if 0 <= index < len(self.frames):
                frame_copy = self.frames[index].copy()
                duration_copy = self.frame_durations[index]
                
                self.frames.insert(index + 1, frame_copy)
                self.frame_durations.insert(index + 1, duration_copy)
                
                self.is_animated = len(self.frames) > 1
                self.logger.info(f"Duplicated frame at index {index}")
                return True
            else:
                self.logger.warning(f"Invalid frame index: {index}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to duplicate frame: {e}")
            return False
    
    def get_frame(self, index: Optional[int] = None) -> Optional[Image.Image]:
        """
        Get a specific frame or current frame.
        
        Args:
            index: Frame index (uses current if None)
            
        Returns:
            PIL Image or None if invalid index
        """
        try:
            frame_index = index if index is not None else self.current_frame_index
            
            if 0 <= frame_index < len(self.frames):
                return self.frames[frame_index].copy()
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to get frame: {e}")
            return None
    
    def set_frame_duration(self, index: int, duration: int) -> bool:
        """
        Set the duration of a specific frame.
        
        Args:
            index: Frame index
            duration: Duration in milliseconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if 0 <= index < len(self.frame_durations):
                self.frame_durations[index] = duration
                self.logger.info(f"Set frame {index} duration to {duration}ms")
                return True
            else:
                self.logger.warning(f"Invalid frame index: {index}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to set frame duration: {e}")
            return False
    
    def resize_all_frames(self, size: Tuple[int, int], resample: int = Image.Resampling.LANCZOS) -> bool:
        """
        Resize all frames to the specified size.
        
        Args:
            size: New size as (width, height)
            resample: Resampling algorithm
            
        Returns:
            True if successful, False otherwise
        """
        try:
            for i, frame in enumerate(self.frames):
                self.frames[i] = frame.resize(size, resample)
            
            self.logger.info(f"Resized all frames to {size}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to resize frames: {e}")
            return False
    
    def apply_filter_to_all_frames(self, filter_func) -> bool:
        """
        Apply a filter function to all frames.
        
        Args:
            filter_func: Function that takes and returns a PIL Image
            
        Returns:
            True if successful, False otherwise
        """
        try:
            for i, frame in enumerate(self.frames):
                filtered_frame = filter_func(frame)
                if filtered_frame:
                    self.frames[i] = filtered_frame
            
            self.logger.info("Applied filter to all frames")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to apply filter to frames: {e}")
            return False
    
    def reverse_animation(self) -> bool:
        """
        Reverse the order of frames in the animation.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.frames.reverse()
            self.frame_durations.reverse()
            
            # Adjust current frame index
            self.current_frame_index = len(self.frames) - 1 - self.current_frame_index
            
            self.logger.info("Reversed animation")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to reverse animation: {e}")
            return False
    
    def create_from_images(self, image_paths: List[str], duration: int = 100) -> bool:
        """
        Create animation from a list of image files.
        
        Args:
            image_paths: List of image file paths
            duration: Frame duration in milliseconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.frames = []
            self.frame_durations = []
            
            for path in image_paths:
                image = Image.open(path)
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                self.frames.append(image)
                self.frame_durations.append(duration)
            
            self.is_animated = len(self.frames) > 1
            self.current_frame_index = 0
            
            self.logger.info(f"Created animation from {len(image_paths)} images")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create animation from images: {e}")
            return False
    
    def export_frames(self, output_dir: str, prefix: str = "frame", format: str = "PNG") -> bool:
        """
        Export all frames as individual images.
        
        Args:
            output_dir: Output directory
            prefix: Filename prefix
            format: Image format
            
        Returns:
            True if successful, False otherwise
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            for i, frame in enumerate(self.frames):
                filename = f"{prefix}_{i:04d}.{format.lower()}"
                filepath = os.path.join(output_dir, filename)
                frame.save(filepath, format)
            
            self.logger.info(f"Exported {len(self.frames)} frames to {output_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export frames: {e}")
            return False
    
    def get_animation_info(self) -> Dict:
        """
        Get information about the current animation.
        
        Returns:
            Dictionary with animation information
        """
        total_duration = sum(self.frame_durations) if self.frame_durations else 0
        
        return {
            'frame_count': len(self.frames),
            'current_frame': self.current_frame_index,
            'is_animated': self.is_animated,
            'total_duration_ms': total_duration,
            'total_duration_s': total_duration / 1000.0,
            'loop_count': self.loop_count,
            'frame_durations': self.frame_durations.copy(),
            'average_fps': 1000.0 / (total_duration / len(self.frames)) if self.frames and total_duration > 0 else 0
        }
    
    def next_frame(self) -> Optional[Image.Image]:
        """Move to and return the next frame."""
        if self.frames:
            self.current_frame_index = (self.current_frame_index + 1) % len(self.frames)
            return self.get_frame()
        return None
    
    def previous_frame(self) -> Optional[Image.Image]:
        """Move to and return the previous frame."""
        if self.frames:
            self.current_frame_index = (self.current_frame_index - 1) % len(self.frames)
            return self.get_frame()
        return None
    
    def goto_frame(self, index: int) -> Optional[Image.Image]:
        """Go to a specific frame."""
        if 0 <= index < len(self.frames):
            self.current_frame_index = index
            return self.get_frame()
        return None
