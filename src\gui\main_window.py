"""
Main Window GUI for AI Image Editor
Provides the primary interface with menu bars, toolbars, canvas, and panels.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import logging
import os

import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.image_manager import ImageManager
from ai.background_remover import BackgroundRemover
from ai.image_enhancer import ImageEnhancer
from animation.gif_manager import GIFManager

class AIImageEditor:
    """Main application window for the AI Image Editor."""
    
    def __init__(self, root):
        """Initialize the main window."""
        self.root = root
        self.logger = logging.getLogger(__name__)
        
        # Initialize core components
        self.image_manager = ImageManager()
        self.background_remover = BackgroundRemover()
        self.image_enhancer = ImageEnhancer()
        self.gif_manager = GIFManager()
        self.current_image = None
        self.zoom_level = 1.0
        self.image_history = []
        self.history_index = -1
        
        # Set up the main window
        self.setup_window()
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        self.logger.info("AI Image Editor initialized successfully")
    
    def setup_window(self):
        """Configure the main window properties."""
        self.root.title("AI Image Editor")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Set window icon (if available)
        try:
            icon_path = os.path.join(os.path.dirname(__file__), '..', '..', 'assets', 'icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            self.logger.warning(f"Could not load window icon: {e}")
    
    def create_widgets(self):
        """Create all GUI widgets."""
        # Create main menu bar
        self.menu_bar = MenuBar(self.root, self)
        
        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        
        # Create toolbar
        self.toolbar = Toolbar(self.main_frame, self)
        
        # Create paned window for resizable panels
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        
        # Left panel for tools and properties
        self.left_panel = ttk.Frame(self.paned_window, width=250)
        self.properties_panel = PropertiesPanel(self.left_panel, self)
        self.layers_panel = LayersPanel(self.left_panel, self)
        
        # Center panel for image canvas
        self.center_panel = ttk.Frame(self.paned_window)
        self.image_canvas = ImageCanvas(self.center_panel, self)
        
        # Status bar
        self.status_bar = ttk.Frame(self.root)
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.zoom_label = ttk.Label(self.status_bar, text="Zoom: 100%")
        self.coords_label = ttk.Label(self.status_bar, text="")
    
    def setup_layout(self):
        """Arrange widgets in the window."""
        # Pack main frame
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Pack toolbar
        self.toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # Configure paned window
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        self.paned_window.add(self.left_panel, weight=0)
        self.paned_window.add(self.center_panel, weight=1)
        
        # Layout left panel
        self.properties_panel.pack(fill=tk.X, padx=5, pady=5)
        ttk.Separator(self.left_panel, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=5)
        self.layers_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Layout center panel
        self.image_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Layout status bar
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_label.pack(side=tk.LEFT, padx=5)
        self.coords_label.pack(side=tk.RIGHT, padx=5)
        self.zoom_label.pack(side=tk.RIGHT, padx=5)
    
    def bind_events(self):
        """Bind keyboard shortcuts and events."""
        # File operations
        self.root.bind('<Control-o>', lambda e: self.open_image())
        self.root.bind('<Control-s>', lambda e: self.save_image())
        self.root.bind('<Control-Shift-s>', lambda e: self.save_image_as())
        self.root.bind('<Control-n>', lambda e: self.new_image())
        
        # Edit operations
        self.root.bind('<Control-z>', lambda e: self.undo())
        self.root.bind('<Control-y>', lambda e: self.redo())
        self.root.bind('<Control-c>', lambda e: self.copy())
        self.root.bind('<Control-v>', lambda e: self.paste())
        
        # View operations
        self.root.bind('<Control-plus>', lambda e: self.zoom_in())
        self.root.bind('<Control-minus>', lambda e: self.zoom_out())
        self.root.bind('<Control-0>', lambda e: self.zoom_fit())
        
        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def open_image(self):
        """Open an image file."""
        file_path = filedialog.askopenfilename(
            title="Open Image",
            filetypes=[
                ("All Images", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff *.webp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("GIF files", "*.gif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.image_manager.load_image(file_path)
                self.current_image = self.image_manager.get_current_image()
                self.image_canvas.display_image(self.current_image)
                self.update_status(f"Opened: {os.path.basename(file_path)}")
                self.properties_panel.update_image_info(self.current_image)
                self.logger.info(f"Opened image: {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to open image: {str(e)}")
                self.logger.error(f"Failed to open image {file_path}: {e}")
    
    def save_image(self):
        """Save the current image."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image to save")
            return
        
        # Implementation will be added in image_manager
        self.update_status("Image saved")
    
    def save_image_as(self):
        """Save the current image with a new name."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image to save")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Save Image As",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("GIF files", "*.gif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.image_manager.save_image(file_path)
                self.update_status(f"Saved: {os.path.basename(file_path)}")
                self.logger.info(f"Saved image: {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save image: {str(e)}")
                self.logger.error(f"Failed to save image {file_path}: {e}")
    
    def new_image(self):
        """Create a new blank image."""
        # This will open a dialog to specify dimensions
        pass
    
    def undo(self):
        """Undo the last operation."""
        if self.image_manager.can_undo():
            self.image_manager.undo()
            self.current_image = self.image_manager.get_current_image()
            self.image_canvas.display_image(self.current_image)
            self.update_status("Undone")
    
    def redo(self):
        """Redo the last undone operation."""
        if self.image_manager.can_redo():
            self.image_manager.redo()
            self.current_image = self.image_manager.get_current_image()
            self.image_canvas.display_image(self.current_image)
            self.update_status("Redone")
    
    def copy(self):
        """Copy current selection to clipboard."""
        pass
    
    def paste(self):
        """Paste from clipboard."""
        pass
    
    def zoom_in(self):
        """Zoom in on the image."""
        self.zoom_level = min(self.zoom_level * 1.2, 10.0)
        self.image_canvas.set_zoom(self.zoom_level)
        self.update_zoom_display()
    
    def zoom_out(self):
        """Zoom out on the image."""
        self.zoom_level = max(self.zoom_level / 1.2, 0.1)
        self.image_canvas.set_zoom(self.zoom_level)
        self.update_zoom_display()
    
    def zoom_fit(self):
        """Fit image to canvas."""
        self.zoom_level = 1.0
        self.image_canvas.fit_to_canvas()
        self.update_zoom_display()
    
    def update_status(self, message):
        """Update the status bar message."""
        self.status_label.config(text=message)
    
    def update_zoom_display(self):
        """Update the zoom level display."""
        self.zoom_label.config(text=f"Zoom: {int(self.zoom_level * 100)}%")
    
    def update_coordinates(self, x, y):
        """Update the coordinates display."""
        if self.current_image:
            self.coords_label.config(text=f"X: {x}, Y: {y}")
    
    def on_closing(self):
        """Handle window closing event."""
        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.logger.info("AI Image Editor closing")
            self.root.destroy()
