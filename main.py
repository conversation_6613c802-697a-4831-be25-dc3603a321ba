#!/usr/bin/env python3
"""
AI Image Editor - Main Application Entry Point
A comprehensive image editor with AI-powered features including background removal,
image enhancement, content generation, animation support, and real-time effects.
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from gui.main_window import AIImageEditor

def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('ai_image_editor.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main application entry point."""
    try:
        # Set up logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Starting AI Image Editor...")
        
        # Create the main application window
        root = tk.Tk()
        app = AIImageEditor(root)
        
        # Start the GUI event loop
        root.mainloop()
        
    except Exception as e:
        error_msg = f"Failed to start AI Image Editor: {str(e)}"
        logging.error(error_msg, exc_info=True)
        messagebox.showerror("Error", error_msg)
        sys.exit(1)

if __name__ == "__main__":
    main()
