"""
Image Manager - Core image processing and management functionality
Handles image loading, saving, transformations, and history management.
"""

import os
import logging
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import numpy as np
from typing import Optional, List, Tuple, Any
import copy

class ImageManager:
    """Manages image operations, history, and transformations."""
    
    def __init__(self):
        """Initialize the image manager."""
        self.logger = logging.getLogger(__name__)
        self.current_image: Optional[Image.Image] = None
        self.original_image: Optional[Image.Image] = None
        self.history: List[Image.Image] = []
        self.history_index: int = -1
        self.max_history_size: int = 20
        self.current_file_path: Optional[str] = None
        
    def load_image(self, file_path: str) -> bool:
        """
        Load an image from file.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Load and convert image
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA'):
                # Create white background for transparency
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image, mask=image.split()[-1])
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            self.current_image = image
            self.original_image = image.copy()
            self.current_file_path = file_path
            
            # Reset history
            self.history = [image.copy()]
            self.history_index = 0
            
            self.logger.info(f"Loaded image: {file_path}, Size: {image.size}, Mode: {image.mode}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load image {file_path}: {e}")
            return False
    
    def save_image(self, file_path: Optional[str] = None, quality: int = 95) -> bool:
        """
        Save the current image.
        
        Args:
            file_path: Path to save the image (uses current path if None)
            quality: JPEG quality (1-100)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            self.logger.warning("No image to save")
            return False
        
        save_path = file_path or self.current_file_path
        if not save_path:
            self.logger.warning("No file path specified for saving")
            return False
        
        try:
            # Determine format from extension
            _, ext = os.path.splitext(save_path)
            ext = ext.lower()
            
            if ext in ['.jpg', '.jpeg']:
                self.current_image.save(save_path, 'JPEG', quality=quality, optimize=True)
            elif ext == '.png':
                self.current_image.save(save_path, 'PNG', optimize=True)
            elif ext == '.gif':
                self.current_image.save(save_path, 'GIF', optimize=True)
            else:
                # Default to PNG
                self.current_image.save(save_path, 'PNG')
            
            if file_path:
                self.current_file_path = file_path
            
            self.logger.info(f"Saved image: {save_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save image {save_path}: {e}")
            return False
    
    def get_current_image(self) -> Optional[Image.Image]:
        """Get the current image."""
        return self.current_image
    
    def get_original_image(self) -> Optional[Image.Image]:
        """Get the original image."""
        return self.original_image
    
    def add_to_history(self, image: Optional[Image.Image] = None):
        """
        Add current image state to history.
        
        Args:
            image: Image to add (uses current image if None)
        """
        if not image:
            image = self.current_image
        
        if not image:
            return
        
        # Remove any history after current index
        self.history = self.history[:self.history_index + 1]
        
        # Add new state
        self.history.append(image.copy())
        self.history_index += 1
        
        # Limit history size
        if len(self.history) > self.max_history_size:
            self.history.pop(0)
            self.history_index -= 1
    
    def can_undo(self) -> bool:
        """Check if undo is possible."""
        return self.history_index > 0
    
    def can_redo(self) -> bool:
        """Check if redo is possible."""
        return self.history_index < len(self.history) - 1
    
    def undo(self) -> bool:
        """Undo the last operation."""
        if not self.can_undo():
            return False
        
        self.history_index -= 1
        self.current_image = self.history[self.history_index].copy()
        return True
    
    def redo(self) -> bool:
        """Redo the last undone operation."""
        if not self.can_redo():
            return False
        
        self.history_index += 1
        self.current_image = self.history[self.history_index].copy()
        return True
    
    def resize_image(self, width: int, height: int, resample: int = Image.Resampling.LANCZOS) -> bool:
        """
        Resize the current image.
        
        Args:
            width: New width
            height: New height
            resample: Resampling algorithm
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            self.add_to_history()
            self.current_image = self.current_image.resize((width, height), resample)
            return True
        except Exception as e:
            self.logger.error(f"Failed to resize image: {e}")
            return False
    
    def rotate_image(self, angle: float, expand: bool = True) -> bool:
        """
        Rotate the current image.
        
        Args:
            angle: Rotation angle in degrees
            expand: Whether to expand the image to fit the rotated content
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            self.add_to_history()
            self.current_image = self.current_image.rotate(angle, expand=expand, fillcolor='white')
            return True
        except Exception as e:
            self.logger.error(f"Failed to rotate image: {e}")
            return False
    
    def crop_image(self, box: Tuple[int, int, int, int]) -> bool:
        """
        Crop the current image.
        
        Args:
            box: (left, top, right, bottom) coordinates
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            self.add_to_history()
            self.current_image = self.current_image.crop(box)
            return True
        except Exception as e:
            self.logger.error(f"Failed to crop image: {e}")
            return False
    
    def flip_image(self, direction: str) -> bool:
        """
        Flip the current image.
        
        Args:
            direction: 'horizontal' or 'vertical'
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            self.add_to_history()
            if direction == 'horizontal':
                self.current_image = self.current_image.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
            elif direction == 'vertical':
                self.current_image = self.current_image.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
            else:
                return False
            return True
        except Exception as e:
            self.logger.error(f"Failed to flip image: {e}")
            return False
    
    def adjust_brightness(self, factor: float) -> bool:
        """
        Adjust image brightness.
        
        Args:
            factor: Brightness factor (1.0 = no change, >1.0 = brighter, <1.0 = darker)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            enhancer = ImageEnhance.Brightness(self.current_image)
            self.current_image = enhancer.enhance(factor)
            return True
        except Exception as e:
            self.logger.error(f"Failed to adjust brightness: {e}")
            return False
    
    def adjust_contrast(self, factor: float) -> bool:
        """
        Adjust image contrast.
        
        Args:
            factor: Contrast factor (1.0 = no change, >1.0 = more contrast, <1.0 = less contrast)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            enhancer = ImageEnhance.Contrast(self.current_image)
            self.current_image = enhancer.enhance(factor)
            return True
        except Exception as e:
            self.logger.error(f"Failed to adjust contrast: {e}")
            return False
    
    def adjust_saturation(self, factor: float) -> bool:
        """
        Adjust image saturation.
        
        Args:
            factor: Saturation factor (1.0 = no change, >1.0 = more saturated, <1.0 = less saturated)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            enhancer = ImageEnhance.Color(self.current_image)
            self.current_image = enhancer.enhance(factor)
            return True
        except Exception as e:
            self.logger.error(f"Failed to adjust saturation: {e}")
            return False
    
    def apply_filter(self, filter_type: str) -> bool:
        """
        Apply a filter to the current image.
        
        Args:
            filter_type: Type of filter to apply
            
        Returns:
            True if successful, False otherwise
        """
        if not self.current_image:
            return False
        
        try:
            self.add_to_history()
            
            if filter_type == 'blur':
                self.current_image = self.current_image.filter(ImageFilter.BLUR)
            elif filter_type == 'sharpen':
                self.current_image = self.current_image.filter(ImageFilter.SHARPEN)
            elif filter_type == 'edge':
                self.current_image = self.current_image.filter(ImageFilter.FIND_EDGES)
            elif filter_type == 'emboss':
                self.current_image = self.current_image.filter(ImageFilter.EMBOSS)
            elif filter_type == 'smooth':
                self.current_image = self.current_image.filter(ImageFilter.SMOOTH)
            elif filter_type == 'detail':
                self.current_image = self.current_image.filter(ImageFilter.DETAIL)
            else:
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to apply filter {filter_type}: {e}")
            return False
    
    def get_image_info(self) -> dict:
        """
        Get information about the current image.
        
        Returns:
            Dictionary with image information
        """
        if not self.current_image:
            return {}
        
        return {
            'size': self.current_image.size,
            'mode': self.current_image.mode,
            'format': getattr(self.current_image, 'format', 'Unknown'),
            'file_path': self.current_file_path,
            'has_transparency': self.current_image.mode in ('RGBA', 'LA', 'P')
        }
