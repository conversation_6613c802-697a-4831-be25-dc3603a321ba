"""
AI Image Enhancer - Intelligent image enhancement using AI and traditional methods
Provides noise reduction, upscaling, color correction, and automatic improvements.
"""

import logging
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import Optional, Tuple, Dict
import cv2

try:
    from skimage import restoration, exposure, filters
    from skimage.util import img_as_float, img_as_ubyte
    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False
    logging.warning("scikit-image not available. Some enhancement features will be limited.")

class ImageEnhancer:
    """AI-powered image enhancement with multiple algorithms."""
    
    def __init__(self):
        """Initialize the image enhancer."""
        self.logger = logging.getLogger(__name__)
    
    def auto_enhance(self, image: Image.Image) -> Optional[Image.Image]:
        """
        Automatically enhance image using multiple techniques.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Enhanced image or None if failed
        """
        try:
            enhanced = image.copy()
            
            # Auto color correction
            enhanced = self.auto_color_correction(enhanced)
            
            # Noise reduction
            enhanced = self.denoise_image(enhanced)
            
            # Sharpening
            enhanced = self.sharpen_image(enhanced, strength=0.3)
            
            # Contrast enhancement
            enhanced = self.enhance_contrast(enhanced, method='adaptive')
            
            self.logger.info("Auto enhancement completed")
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Auto enhancement failed: {e}")
            return None
    
    def denoise_image(self, image: Image.Image, method: str = 'bilateral') -> Optional[Image.Image]:
        """
        Remove noise from image using various methods.
        
        Args:
            image: Input PIL Image
            method: Denoising method ('bilateral', 'gaussian', 'median', 'non_local_means')
            
        Returns:
            Denoised image or None if failed
        """
        try:
            img_array = np.array(image)
            
            if method == 'bilateral':
                # Bilateral filter preserves edges while reducing noise
                denoised = cv2.bilateralFilter(img_array, 9, 75, 75)
            elif method == 'gaussian':
                # Gaussian blur for simple noise reduction
                denoised = cv2.GaussianBlur(img_array, (5, 5), 0)
            elif method == 'median':
                # Median filter for salt-and-pepper noise
                denoised = cv2.medianBlur(img_array, 5)
            elif method == 'non_local_means':
                # Non-local means denoising (more advanced)
                if len(img_array.shape) == 3:
                    denoised = cv2.fastNlMeansDenoisingColored(img_array, None, 10, 10, 7, 21)
                else:
                    denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
            else:
                return image
            
            result_image = Image.fromarray(denoised)
            self.logger.info(f"Denoising completed using {method} method")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Denoising failed: {e}")
            return None
    
    def sharpen_image(self, image: Image.Image, strength: float = 0.5) -> Optional[Image.Image]:
        """
        Sharpen image using unsharp masking.
        
        Args:
            image: Input PIL Image
            strength: Sharpening strength (0.0 to 2.0)
            
        Returns:
            Sharpened image or None if failed
        """
        try:
            # Convert to array
            img_array = np.array(image).astype(np.float32) / 255.0
            
            # Create Gaussian blur
            blurred = cv2.GaussianBlur(img_array, (0, 0), 2.0)
            
            # Unsharp mask
            sharpened = img_array + strength * (img_array - blurred)
            
            # Clip values and convert back
            sharpened = np.clip(sharpened, 0, 1)
            sharpened = (sharpened * 255).astype(np.uint8)
            
            result_image = Image.fromarray(sharpened)
            self.logger.info(f"Sharpening completed with strength {strength}")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Sharpening failed: {e}")
            return None
    
    def enhance_contrast(self, image: Image.Image, method: str = 'adaptive') -> Optional[Image.Image]:
        """
        Enhance image contrast using various methods.
        
        Args:
            image: Input PIL Image
            method: Enhancement method ('adaptive', 'histogram', 'clahe')
            
        Returns:
            Enhanced image or None if failed
        """
        try:
            if method == 'adaptive':
                # Simple adaptive contrast enhancement
                enhancer = ImageEnhance.Contrast(image)
                return enhancer.enhance(1.2)
            
            elif method == 'histogram' and SKIMAGE_AVAILABLE:
                # Histogram equalization
                img_array = np.array(image)
                if len(img_array.shape) == 3:
                    # For color images, work in YUV space
                    yuv = cv2.cvtColor(img_array, cv2.COLOR_RGB2YUV)
                    yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
                    enhanced = cv2.cvtColor(yuv, cv2.COLOR_YUV2RGB)
                else:
                    enhanced = cv2.equalizeHist(img_array)
                
                return Image.fromarray(enhanced)
            
            elif method == 'clahe':
                # Contrast Limited Adaptive Histogram Equalization
                img_array = np.array(image)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                
                if len(img_array.shape) == 3:
                    # For color images, work in LAB space
                    lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
                    lab[:, :, 0] = clahe.apply(lab[:, :, 0])
                    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
                else:
                    enhanced = clahe.apply(img_array)
                
                return Image.fromarray(enhanced)
            
            else:
                return image
                
        except Exception as e:
            self.logger.error(f"Contrast enhancement failed: {e}")
            return None
    
    def auto_color_correction(self, image: Image.Image) -> Optional[Image.Image]:
        """
        Automatically correct image colors.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Color-corrected image or None if failed
        """
        try:
            img_array = np.array(image).astype(np.float32)
            
            # White balance correction using gray world assumption
            mean_r = np.mean(img_array[:, :, 0])
            mean_g = np.mean(img_array[:, :, 1])
            mean_b = np.mean(img_array[:, :, 2])
            
            # Calculate scaling factors
            gray_mean = (mean_r + mean_g + mean_b) / 3
            scale_r = gray_mean / mean_r if mean_r > 0 else 1
            scale_g = gray_mean / mean_g if mean_g > 0 else 1
            scale_b = gray_mean / mean_b if mean_b > 0 else 1
            
            # Apply scaling
            img_array[:, :, 0] *= scale_r
            img_array[:, :, 1] *= scale_g
            img_array[:, :, 2] *= scale_b
            
            # Clip values
            img_array = np.clip(img_array, 0, 255).astype(np.uint8)
            
            result_image = Image.fromarray(img_array)
            self.logger.info("Auto color correction completed")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Color correction failed: {e}")
            return None
    
    def upscale_image(self, image: Image.Image, scale_factor: float = 2.0, 
                     method: str = 'lanczos') -> Optional[Image.Image]:
        """
        Upscale image using various interpolation methods.
        
        Args:
            image: Input PIL Image
            scale_factor: Scaling factor
            method: Interpolation method ('lanczos', 'cubic', 'linear')
            
        Returns:
            Upscaled image or None if failed
        """
        try:
            width, height = image.size
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            
            if method == 'lanczos':
                resample = Image.Resampling.LANCZOS
            elif method == 'cubic':
                resample = Image.Resampling.BICUBIC
            elif method == 'linear':
                resample = Image.Resampling.BILINEAR
            else:
                resample = Image.Resampling.LANCZOS
            
            upscaled = image.resize((new_width, new_height), resample)
            
            self.logger.info(f"Image upscaled by {scale_factor}x using {method}")
            return upscaled
            
        except Exception as e:
            self.logger.error(f"Upscaling failed: {e}")
            return None
    
    def adjust_gamma(self, image: Image.Image, gamma: float = 1.0) -> Optional[Image.Image]:
        """
        Adjust image gamma for brightness correction.
        
        Args:
            image: Input PIL Image
            gamma: Gamma value (< 1.0 = brighter, > 1.0 = darker)
            
        Returns:
            Gamma-corrected image or None if failed
        """
        try:
            img_array = np.array(image).astype(np.float32) / 255.0
            
            # Apply gamma correction
            corrected = np.power(img_array, gamma)
            
            # Convert back to uint8
            corrected = (corrected * 255).astype(np.uint8)
            
            result_image = Image.fromarray(corrected)
            self.logger.info(f"Gamma correction applied: {gamma}")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Gamma correction failed: {e}")
            return None
    
    def enhance_details(self, image: Image.Image, strength: float = 0.5) -> Optional[Image.Image]:
        """
        Enhance image details using high-pass filtering.
        
        Args:
            image: Input PIL Image
            strength: Enhancement strength (0.0 to 2.0)
            
        Returns:
            Detail-enhanced image or None if failed
        """
        try:
            # Convert to grayscale for detail extraction
            gray = image.convert('L')
            gray_array = np.array(gray).astype(np.float32)
            
            # Create high-pass filter
            blurred = cv2.GaussianBlur(gray_array, (0, 0), 3.0)
            high_pass = gray_array - blurred
            
            # Apply to original image
            img_array = np.array(image).astype(np.float32)
            
            for i in range(3):  # Apply to each color channel
                img_array[:, :, i] += high_pass * strength
            
            # Clip and convert back
            img_array = np.clip(img_array, 0, 255).astype(np.uint8)
            
            result_image = Image.fromarray(img_array)
            self.logger.info(f"Detail enhancement completed with strength {strength}")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Detail enhancement failed: {e}")
            return None
    
    def reduce_artifacts(self, image: Image.Image) -> Optional[Image.Image]:
        """
        Reduce compression artifacts and improve image quality.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Artifact-reduced image or None if failed
        """
        try:
            # Apply mild bilateral filtering to reduce artifacts while preserving edges
            img_array = np.array(image)
            filtered = cv2.bilateralFilter(img_array, 5, 50, 50)
            
            result_image = Image.fromarray(filtered)
            self.logger.info("Artifact reduction completed")
            return result_image
            
        except Exception as e:
            self.logger.error(f"Artifact reduction failed: {e}")
            return None
    
    def get_image_quality_score(self, image: Image.Image) -> float:
        """
        Calculate a quality score for the image.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Quality score (0.0 to 1.0, higher is better)
        """
        try:
            img_array = np.array(image.convert('L'))
            
            # Calculate sharpness using Laplacian variance
            laplacian_var = cv2.Laplacian(img_array, cv2.CV_64F).var()
            
            # Normalize to 0-1 range (rough approximation)
            quality_score = min(laplacian_var / 1000.0, 1.0)
            
            return quality_score
            
        except Exception as e:
            self.logger.error(f"Quality score calculation failed: {e}")
            return 0.0
