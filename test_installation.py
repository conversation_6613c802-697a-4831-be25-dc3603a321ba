#!/usr/bin/env python3
"""
Test Installation Script for AI Image Editor
Verifies that all dependencies are properly installed and the application can start.
"""

import sys
import logging
import traceback

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    # Core Python libraries
    try:
        import tkinter as tk
        print("✓ tkinter (GUI framework)")
    except ImportError as e:
        print(f"✗ tkinter: {e}")
        return False
    
    # Image processing libraries
    try:
        from PIL import Image, ImageTk
        print("✓ Pillow (PIL)")
    except ImportError as e:
        print(f"✗ Pillow: {e}")
        return False
    
    try:
        import cv2
        print("✓ OpenCV")
    except ImportError as e:
        print(f"✗ OpenCV: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ NumPy")
    except ImportError as e:
        print(f"✗ NumPy: {e}")
        return False
    
    # AI libraries (optional but recommended)
    try:
        import rembg
        print("✓ rembg (AI background removal)")
    except ImportError as e:
        print(f"⚠ rembg (optional): {e}")
    
    try:
        import torch
        print("✓ PyTorch")
    except ImportError as e:
        print(f"⚠ PyTorch (optional): {e}")
    
    try:
        from skimage import restoration
        print("✓ scikit-image")
    except ImportError as e:
        print(f"⚠ scikit-image (optional): {e}")
    
    try:
        import imageio
        print("✓ imageio")
    except ImportError as e:
        print(f"⚠ imageio (optional): {e}")
    
    return True

def test_core_modules():
    """Test if our core modules can be imported."""
    print("\nTesting core modules...")
    
    try:
        sys.path.insert(0, 'src')
        
        from core.image_manager import ImageManager
        print("✓ ImageManager")
        
        from ai.background_remover import BackgroundRemover
        print("✓ BackgroundRemover")
        
        from ai.image_enhancer import ImageEnhancer
        print("✓ ImageEnhancer")
        
        from animation.gif_manager import GIFManager
        print("✓ GIFManager")
        
        from gui.ai_image_editor import AIImageEditor
        print("✓ AIImageEditor")
        
        return True
        
    except Exception as e:
        print(f"✗ Core modules: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality without GUI."""
    print("\nTesting basic functionality...")
    
    try:
        sys.path.insert(0, 'src')
        
        # Test ImageManager
        from core.image_manager import ImageManager
        manager = ImageManager()
        print("✓ ImageManager initialization")
        
        # Test BackgroundRemover
        from ai.background_remover import BackgroundRemover
        bg_remover = BackgroundRemover()
        models = bg_remover.get_available_models()
        print(f"✓ BackgroundRemover - Available models: {models}")
        
        # Test ImageEnhancer
        from ai.image_enhancer import ImageEnhancer
        enhancer = ImageEnhancer()
        print("✓ ImageEnhancer initialization")
        
        # Test GIFManager
        from animation.gif_manager import GIFManager
        gif_manager = GIFManager()
        print("✓ GIFManager initialization")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality: {e}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """Test if GUI can be created (without showing it)."""
    print("\nTesting GUI creation...")
    
    try:
        import tkinter as tk
        sys.path.insert(0, 'src')
        
        # Create root window (but don't show it)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        from gui.ai_image_editor import AIImageEditor
        app = AIImageEditor(root)
        print("✓ GUI creation successful")
        
        # Clean up
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ GUI creation: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("AI Image Editor - Installation Test")
    print("=" * 40)
    
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Suppress info logs during testing
    
    tests = [
        ("Import Test", test_imports),
        ("Core Modules Test", test_core_modules),
        ("Basic Functionality Test", test_basic_functionality),
        ("GUI Creation Test", test_gui_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The AI Image Editor is ready to use.")
        print("Run 'python main.py' to start the application.")
        return 0
    else:
        print(f"\n⚠ {total - passed} test(s) failed. Please check the installation.")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    sys.exit(main())
