#!/usr/bin/env python3
"""
AI Image Editor - Feature Demonstration
Shows how to use the AI features programmatically without the GUI.
"""

import sys
import os
from PIL import Image
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.image_manager import ImageManager
from ai.background_remover import BackgroundRemover
from ai.image_enhancer import ImageEnhancer
from animation.gif_manager import GIFManager

def setup_logging():
    """Set up logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def create_sample_image():
    """Create a sample image for testing."""
    # Create a simple test image
    img = Image.new('RGB', (400, 300), color='lightblue')
    
    # Add some content (simple colored rectangles)
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # Draw some shapes
    draw.rectangle([50, 50, 150, 150], fill='red')
    draw.rectangle([200, 100, 300, 200], fill='green')
    draw.ellipse([100, 180, 200, 250], fill='yellow')
    
    return img

def demo_image_manager():
    """Demonstrate ImageManager functionality."""
    print("\n=== Image Manager Demo ===")
    
    manager = ImageManager()
    
    # Create and load a sample image
    sample_img = create_sample_image()
    
    # Simulate loading (we'll use the sample image directly)
    manager.current_image = sample_img
    manager.original_image = sample_img.copy()
    manager.add_to_history()
    
    print(f"Original image size: {sample_img.size}")
    
    # Test resize
    if manager.resize_image(200, 150):
        resized = manager.get_current_image()
        print(f"Resized image size: {resized.size}")
    
    # Test rotation
    manager.add_to_history()
    if manager.rotate_image(45):
        rotated = manager.get_current_image()
        print(f"Rotated image size: {rotated.size}")
    
    # Test filters
    manager.add_to_history()
    if manager.apply_filter('blur'):
        print("Applied blur filter")
    
    # Test undo/redo
    print(f"Can undo: {manager.can_undo()}")
    if manager.undo():
        print("Undone last operation")
    
    print(f"Can redo: {manager.can_redo()}")
    if manager.redo():
        print("Redone last operation")
    
    return manager.get_current_image()

def demo_background_remover():
    """Demonstrate BackgroundRemover functionality."""
    print("\n=== Background Remover Demo ===")
    
    remover = BackgroundRemover()
    
    # Get available models
    models = remover.get_available_models()
    print(f"Available background removal models: {models}")
    
    # Create a sample image
    sample_img = create_sample_image()
    
    # Try color threshold method (most reliable for demo)
    print("Testing color threshold background removal...")
    result = remover.remove_background_color_threshold(
        sample_img, 
        target_color=(173, 216, 230),  # lightblue background
        threshold=50
    )
    
    if result:
        print("✓ Color threshold background removal successful")
        print(f"Result image mode: {result.mode}")
        return result
    else:
        print("✗ Background removal failed")
        return sample_img

def demo_image_enhancer():
    """Demonstrate ImageEnhancer functionality."""
    print("\n=== Image Enhancer Demo ===")
    
    enhancer = ImageEnhancer()
    
    # Create a sample image
    sample_img = create_sample_image()
    
    # Test denoising
    print("Testing image denoising...")
    denoised = enhancer.denoise_image(sample_img, method='bilateral')
    if denoised:
        print("✓ Denoising successful")
    
    # Test sharpening
    print("Testing image sharpening...")
    sharpened = enhancer.sharpen_image(sample_img, strength=0.5)
    if sharpened:
        print("✓ Sharpening successful")
    
    # Test contrast enhancement
    print("Testing contrast enhancement...")
    enhanced = enhancer.enhance_contrast(sample_img, method='adaptive')
    if enhanced:
        print("✓ Contrast enhancement successful")
    
    # Test auto enhancement
    print("Testing auto enhancement...")
    auto_enhanced = enhancer.auto_enhance(sample_img)
    if auto_enhanced:
        print("✓ Auto enhancement successful")
        return auto_enhanced
    
    return sample_img

def demo_gif_manager():
    """Demonstrate GIFManager functionality."""
    print("\n=== GIF Manager Demo ===")
    
    gif_manager = GIFManager()
    
    # Create multiple sample images for animation
    frames = []
    colors = ['red', 'green', 'blue', 'yellow', 'purple']
    
    for i, color in enumerate(colors):
        img = Image.new('RGB', (200, 200), color='white')
        from PIL import ImageDraw
        draw = ImageDraw.Draw(img)
        
        # Draw a moving circle
        x = 20 + i * 30
        y = 50 + i * 20
        draw.ellipse([x, y, x+50, y+50], fill=color)
        
        frames.append(img)
    
    # Add frames to animation
    for i, frame in enumerate(frames):
        gif_manager.add_frame(frame, duration=200)  # 200ms per frame
    
    print(f"Created animation with {len(gif_manager.frames)} frames")
    
    # Test animation info
    info = gif_manager.get_animation_info()
    print(f"Animation info: {info}")
    
    # Test frame navigation
    print("Testing frame navigation...")
    current_frame = gif_manager.get_frame()
    if current_frame:
        print(f"Current frame size: {current_frame.size}")
    
    next_frame = gif_manager.next_frame()
    if next_frame:
        print("✓ Next frame navigation successful")
    
    prev_frame = gif_manager.previous_frame()
    if prev_frame:
        print("✓ Previous frame navigation successful")
    
    # Test frame operations
    print("Testing frame duplication...")
    if gif_manager.duplicate_frame(0):
        print("✓ Frame duplication successful")
    
    return gif_manager

def save_demo_results(enhanced_img, bg_removed_img, gif_manager):
    """Save demo results to files."""
    print("\n=== Saving Demo Results ===")
    
    # Create output directory
    output_dir = "demo_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Save enhanced image
        enhanced_img.save(os.path.join(output_dir, "enhanced_image.png"))
        print("✓ Saved enhanced image")
        
        # Save background removed image
        bg_removed_img.save(os.path.join(output_dir, "background_removed.png"))
        print("✓ Saved background removed image")
        
        # Save animation as GIF
        gif_path = os.path.join(output_dir, "demo_animation.gif")
        if gif_manager.save_as_gif(gif_path):
            print("✓ Saved demo animation")
        
        print(f"\nDemo results saved to '{output_dir}' directory")
        
    except Exception as e:
        print(f"✗ Error saving results: {e}")

def main():
    """Run the AI features demonstration."""
    print("AI Image Editor - Feature Demonstration")
    print("=" * 50)
    
    setup_logging()
    
    try:
        # Demo each component
        enhanced_img = demo_image_manager()
        bg_removed_img = demo_background_remover()
        enhanced_img = demo_image_enhancer()
        gif_manager = demo_gif_manager()
        
        # Save results
        save_demo_results(enhanced_img, bg_removed_img, gif_manager)
        
        print("\n" + "=" * 50)
        print("🎉 Demo completed successfully!")
        print("All AI features are working correctly.")
        print("\nTo use the full GUI application, run: python main.py")
        
    except Exception as e:
        print(f"\n✗ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
