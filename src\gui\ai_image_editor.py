"""
Complete AI Image Editor - Integrated GUI with all AI features
Main application window with comprehensive image editing and AI capabilities.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
from PIL import Image, ImageTk
import logging
import os
import sys
import threading

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from core.image_manager import ImageManager
from ai.background_remover import BackgroundRemover
from ai.image_enhancer import ImageEnhancer
from animation.gif_manager import GIFManager

class AIImageEditor:
    """Complete AI Image Editor with integrated features."""
    
    def __init__(self, root):
        """Initialize the AI Image Editor."""
        self.root = root
        self.logger = logging.getLogger(__name__)
        
        # Initialize core components
        self.image_manager = ImageManager()
        self.background_remover = BackgroundRemover()
        self.image_enhancer = ImageEnhancer()
        self.gif_manager = GIFManager()
        
        # UI state
        self.current_image = None
        self.zoom_level = 1.0
        self.canvas_image = None
        
        # Set up the interface
        self.setup_window()
        self.create_menu()
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        self.logger.info("AI Image Editor initialized successfully")
    
    def setup_window(self):
        """Configure the main window."""
        self.root.title("AI Image Editor - Advanced Image Processing")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
    
    def create_menu(self):
        """Create the main menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Image", command=self.open_image, accelerator="Ctrl+O")
        file_menu.add_command(label="Open GIF/Animation", command=self.open_animation)
        file_menu.add_separator()
        file_menu.add_command(label="Save", command=self.save_image, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As", command=self.save_image_as, accelerator="Ctrl+Shift+S")
        file_menu.add_command(label="Export GIF", command=self.export_gif)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="Redo", command=self.redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="Resize Image", command=self.resize_image)
        edit_menu.add_command(label="Rotate Image", command=self.rotate_image)
        edit_menu.add_command(label="Flip Horizontal", command=lambda: self.flip_image('horizontal'))
        edit_menu.add_command(label="Flip Vertical", command=lambda: self.flip_image('vertical'))
        
        # AI menu
        ai_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="AI Tools", menu=ai_menu)
        ai_menu.add_command(label="Remove Background", command=self.ai_remove_background)
        ai_menu.add_command(label="Auto Enhance", command=self.ai_auto_enhance)
        ai_menu.add_command(label="Denoise Image", command=self.ai_denoise)
        ai_menu.add_command(label="Sharpen Image", command=self.ai_sharpen)
        ai_menu.add_command(label="Upscale Image", command=self.ai_upscale)
        ai_menu.add_command(label="Color Correction", command=self.ai_color_correct)
        
        # Filters menu
        filters_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Filters", menu=filters_menu)
        filters_menu.add_command(label="Blur", command=lambda: self.apply_filter("blur"))
        filters_menu.add_command(label="Sharpen", command=lambda: self.apply_filter("sharpen"))
        filters_menu.add_command(label="Edge Detect", command=lambda: self.apply_filter("edge"))
        filters_menu.add_command(label="Emboss", command=lambda: self.apply_filter("emboss"))
        
        # Animation menu
        animation_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Animation", menu=animation_menu)
        animation_menu.add_command(label="Add Frame", command=self.add_frame)
        animation_menu.add_command(label="Remove Frame", command=self.remove_frame)
        animation_menu.add_command(label="Duplicate Frame", command=self.duplicate_frame)
        animation_menu.add_command(label="Play Animation", command=self.play_animation)
        animation_menu.add_command(label="Stop Animation", command=self.stop_animation)
    
    def create_widgets(self):
        """Create all GUI widgets."""
        # Main container
        self.main_frame = ttk.Frame(self.root)
        
        # Toolbar
        self.create_toolbar()
        
        # Paned window for layout
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        
        # Left panel
        self.left_panel = ttk.Frame(self.paned_window, width=300)
        self.create_left_panel()
        
        # Center panel (image canvas)
        self.center_panel = ttk.Frame(self.paned_window)
        self.create_canvas()
        
        # Status bar
        self.create_status_bar()
    
    def create_toolbar(self):
        """Create the main toolbar."""
        self.toolbar = ttk.Frame(self.main_frame)
        
        # File operations
        ttk.Button(self.toolbar, text="Open", command=self.open_image).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Save", command=self.save_image).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # AI tools
        ttk.Button(self.toolbar, text="Remove BG", command=self.ai_remove_background).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Enhance", command=self.ai_auto_enhance).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Denoise", command=self.ai_denoise).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # Zoom controls
        ttk.Button(self.toolbar, text="Zoom In", command=self.zoom_in).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Zoom Out", command=self.zoom_out).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Fit", command=self.zoom_fit).pack(side=tk.LEFT, padx=2)
    
    def create_left_panel(self):
        """Create the left control panel."""
        # Image info
        info_frame = ttk.LabelFrame(self.left_panel, text="Image Information")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.info_label = ttk.Label(info_frame, text="No image loaded", wraplength=250)
        self.info_label.pack(padx=5, pady=5)
        
        # Adjustments
        adj_frame = ttk.LabelFrame(self.left_panel, text="Adjustments")
        adj_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Brightness
        ttk.Label(adj_frame, text="Brightness:").pack(anchor=tk.W, padx=5)
        self.brightness_var = tk.DoubleVar(value=0)
        brightness_scale = ttk.Scale(adj_frame, from_=-100, to=100, variable=self.brightness_var,
                                   command=self.on_brightness_change)
        brightness_scale.pack(fill=tk.X, padx=5, pady=2)
        
        # Contrast
        ttk.Label(adj_frame, text="Contrast:").pack(anchor=tk.W, padx=5)
        self.contrast_var = tk.DoubleVar(value=0)
        contrast_scale = ttk.Scale(adj_frame, from_=-100, to=100, variable=self.contrast_var,
                                 command=self.on_contrast_change)
        contrast_scale.pack(fill=tk.X, padx=5, pady=2)
        
        # Animation controls
        anim_frame = ttk.LabelFrame(self.left_panel, text="Animation")
        anim_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.frame_label = ttk.Label(anim_frame, text="Frame: 0/0")
        self.frame_label.pack(padx=5, pady=2)
        
        frame_controls = ttk.Frame(anim_frame)
        frame_controls.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(frame_controls, text="◀", command=self.prev_frame, width=3).pack(side=tk.LEFT)
        ttk.Button(frame_controls, text="▶", command=self.next_frame, width=3).pack(side=tk.LEFT)
        ttk.Button(frame_controls, text="⏸", command=self.stop_animation, width=3).pack(side=tk.LEFT)
        ttk.Button(frame_controls, text="⏯", command=self.play_animation, width=3).pack(side=tk.LEFT)
    
    def create_canvas(self):
        """Create the main image canvas."""
        canvas_frame = ttk.Frame(self.center_panel)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg='gray90')
        
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.canvas.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # Bind events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<Motion>", self.on_canvas_motion)
        self.canvas.bind("<MouseWheel>", self.on_canvas_scroll)
    
    def create_status_bar(self):
        """Create the status bar."""
        self.status_bar = ttk.Frame(self.root)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        self.coords_label = ttk.Label(self.status_bar, text="")
        self.coords_label.pack(side=tk.RIGHT, padx=5)
        
        self.zoom_label = ttk.Label(self.status_bar, text="Zoom: 100%")
        self.zoom_label.pack(side=tk.RIGHT, padx=5)
    
    def setup_layout(self):
        """Arrange all widgets."""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.toolbar.pack(fill=tk.X, pady=(0, 5))
        
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        self.paned_window.add(self.left_panel, weight=0)
        self.paned_window.add(self.center_panel, weight=1)
        
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def bind_events(self):
        """Bind keyboard shortcuts."""
        self.root.bind('<Control-o>', lambda e: self.open_image())
        self.root.bind('<Control-s>', lambda e: self.save_image())
        self.root.bind('<Control-Shift-s>', lambda e: self.save_image_as())
        self.root.bind('<Control-z>', lambda e: self.undo())
        self.root.bind('<Control-y>', lambda e: self.redo())
        self.root.bind('<Control-plus>', lambda e: self.zoom_in())
        self.root.bind('<Control-minus>', lambda e: self.zoom_out())
        self.root.bind('<Control-0>', lambda e: self.zoom_fit())
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def open_image(self):
        """Open an image file."""
        file_path = filedialog.askopenfilename(
            title="Open Image",
            filetypes=[
                ("All Images", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff *.webp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("GIF files", "*.gif"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.lower().endswith('.gif'):
                    # Load as animation
                    if self.gif_manager.load_animated_image(file_path):
                        self.current_image = self.gif_manager.get_frame()
                        self.update_animation_info()
                else:
                    # Load as static image
                    if self.image_manager.load_image(file_path):
                        self.current_image = self.image_manager.get_current_image()

                if self.current_image:
                    self.display_image()
                    self.update_image_info()
                    self.update_status(f"Opened: {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to open image: {str(e)}")
                self.logger.error(f"Failed to open image {file_path}: {e}")

    def open_animation(self):
        """Open an animated image or create animation from multiple images."""
        file_paths = filedialog.askopenfilenames(
            title="Open Animation or Multiple Images",
            filetypes=[
                ("GIF files", "*.gif"),
                ("All Images", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )

        if file_paths:
            try:
                if len(file_paths) == 1 and file_paths[0].lower().endswith('.gif'):
                    # Single GIF file
                    if self.gif_manager.load_animated_image(file_paths[0]):
                        self.current_image = self.gif_manager.get_frame()
                        self.update_animation_info()
                        self.display_image()
                        self.update_status(f"Opened animation: {os.path.basename(file_paths[0])}")
                else:
                    # Multiple images - create animation
                    if self.gif_manager.create_from_images(list(file_paths)):
                        self.current_image = self.gif_manager.get_frame()
                        self.update_animation_info()
                        self.display_image()
                        self.update_status(f"Created animation from {len(file_paths)} images")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to open animation: {str(e)}")

    def save_image(self):
        """Save the current image."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image to save")
            return

        if self.image_manager.current_file_path:
            self.image_manager.save_image()
            self.update_status("Image saved")
        else:
            self.save_image_as()

    def save_image_as(self):
        """Save the current image with a new name."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image to save")
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Image As",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("GIF files", "*.gif"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # Update image manager with current image
                self.image_manager.current_image = self.current_image
                self.image_manager.save_image(file_path)
                self.update_status(f"Saved: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save image: {str(e)}")

    def export_gif(self):
        """Export current animation as GIF."""
        if not self.gif_manager.frames:
            messagebox.showwarning("Warning", "No animation to export")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export GIF",
            defaultextension=".gif",
            filetypes=[("GIF files", "*.gif")]
        )

        if file_path:
            try:
                self.gif_manager.save_as_gif(file_path)
                self.update_status(f"Exported GIF: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export GIF: {str(e)}")

    def display_image(self):
        """Display the current image on canvas."""
        if not self.current_image:
            return

        try:
            # Calculate display size
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                self.root.after(100, self.display_image)
                return

            # Resize image for display
            img_width, img_height = self.current_image.size
            display_width = int(img_width * self.zoom_level)
            display_height = int(img_height * self.zoom_level)

            if display_width > 0 and display_height > 0:
                display_image = self.current_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
                self.canvas_image = ImageTk.PhotoImage(display_image)

                # Clear canvas and display image
                self.canvas.delete("all")
                self.canvas.create_image(canvas_width//2, canvas_height//2,
                                       image=self.canvas_image, anchor=tk.CENTER)

                # Update scroll region
                self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        except Exception as e:
            self.logger.error(f"Failed to display image: {e}")

    def update_image_info(self):
        """Update the image information display."""
        if self.current_image:
            width, height = self.current_image.size
            mode = self.current_image.mode
            info_text = f"Size: {width}x{height}\nMode: {mode}"

            if hasattr(self.current_image, 'format'):
                info_text += f"\nFormat: {self.current_image.format}"

            self.info_label.config(text=info_text)
        else:
            self.info_label.config(text="No image loaded")

    def update_animation_info(self):
        """Update animation frame information."""
        if self.gif_manager.frames:
            current = self.gif_manager.current_frame_index + 1
            total = len(self.gif_manager.frames)
            self.frame_label.config(text=f"Frame: {current}/{total}")
        else:
            self.frame_label.config(text="Frame: 0/0")

    # AI functionality methods
    def ai_remove_background(self):
        """Remove background using AI."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        self.update_status("Removing background...")
        self.root.config(cursor="wait")

        def process():
            try:
                result = self.background_remover.auto_remove_background(self.current_image)
                if result:
                    self.current_image = result
                    self.root.after(0, lambda: [
                        self.display_image(),
                        self.update_status("Background removed successfully"),
                        self.root.config(cursor=""),
                        self.add_to_history()
                    ])
                else:
                    self.root.after(0, lambda: [
                        messagebox.showerror("Error", "Failed to remove background"),
                        self.update_status("Background removal failed"),
                        self.root.config(cursor="")
                    ])
            except Exception as e:
                self.root.after(0, lambda: [
                    messagebox.showerror("Error", f"Background removal error: {str(e)}"),
                    self.update_status("Background removal failed"),
                    self.root.config(cursor="")
                ])

        threading.Thread(target=process, daemon=True).start()

    def ai_auto_enhance(self):
        """Auto enhance image using AI."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        self.update_status("Enhancing image...")
        self.root.config(cursor="wait")

        def process():
            try:
                result = self.image_enhancer.auto_enhance(self.current_image)
                if result:
                    self.current_image = result
                    self.root.after(0, lambda: [
                        self.display_image(),
                        self.update_status("Image enhanced successfully"),
                        self.root.config(cursor=""),
                        self.add_to_history()
                    ])
                else:
                    self.root.after(0, lambda: [
                        messagebox.showerror("Error", "Failed to enhance image"),
                        self.update_status("Enhancement failed"),
                        self.root.config(cursor="")
                    ])
            except Exception as e:
                self.root.after(0, lambda: [
                    messagebox.showerror("Error", f"Enhancement error: {str(e)}"),
                    self.update_status("Enhancement failed"),
                    self.root.config(cursor="")
                ])

        threading.Thread(target=process, daemon=True).start()

    def ai_denoise(self):
        """Denoise image using AI."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        method = simpledialog.askstring("Denoise Method",
                                      "Enter method (bilateral, gaussian, median, non_local_means):",
                                      initialvalue="bilateral")
        if not method:
            return

        self.update_status("Denoising image...")
        self.root.config(cursor="wait")

        def process():
            try:
                result = self.image_enhancer.denoise_image(self.current_image, method)
                if result:
                    self.current_image = result
                    self.root.after(0, lambda: [
                        self.display_image(),
                        self.update_status(f"Image denoised using {method}"),
                        self.root.config(cursor=""),
                        self.add_to_history()
                    ])
                else:
                    self.root.after(0, lambda: [
                        messagebox.showerror("Error", "Failed to denoise image"),
                        self.update_status("Denoising failed"),
                        self.root.config(cursor="")
                    ])
            except Exception as e:
                self.root.after(0, lambda: [
                    messagebox.showerror("Error", f"Denoising error: {str(e)}"),
                    self.update_status("Denoising failed"),
                    self.root.config(cursor="")
                ])

        threading.Thread(target=process, daemon=True).start()

    def ai_sharpen(self):
        """Sharpen image using AI."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        strength = simpledialog.askfloat("Sharpen Strength",
                                       "Enter strength (0.1 to 2.0):",
                                       initialvalue=0.5, minvalue=0.1, maxvalue=2.0)
        if strength is None:
            return

        self.update_status("Sharpening image...")

        try:
            result = self.image_enhancer.sharpen_image(self.current_image, strength)
            if result:
                self.current_image = result
                self.display_image()
                self.update_status(f"Image sharpened (strength: {strength})")
                self.add_to_history()
            else:
                messagebox.showerror("Error", "Failed to sharpen image")
        except Exception as e:
            messagebox.showerror("Error", f"Sharpening error: {str(e)}")

    def ai_upscale(self):
        """Upscale image using AI."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        scale = simpledialog.askfloat("Upscale Factor",
                                    "Enter scale factor (1.1 to 4.0):",
                                    initialvalue=2.0, minvalue=1.1, maxvalue=4.0)
        if scale is None:
            return

        self.update_status("Upscaling image...")
        self.root.config(cursor="wait")

        def process():
            try:
                result = self.image_enhancer.upscale_image(self.current_image, scale)
                if result:
                    self.current_image = result
                    self.root.after(0, lambda: [
                        self.display_image(),
                        self.update_image_info(),
                        self.update_status(f"Image upscaled by {scale}x"),
                        self.root.config(cursor=""),
                        self.add_to_history()
                    ])
                else:
                    self.root.after(0, lambda: [
                        messagebox.showerror("Error", "Failed to upscale image"),
                        self.update_status("Upscaling failed"),
                        self.root.config(cursor="")
                    ])
            except Exception as e:
                self.root.after(0, lambda: [
                    messagebox.showerror("Error", f"Upscaling error: {str(e)}"),
                    self.update_status("Upscaling failed"),
                    self.root.config(cursor="")
                ])

        threading.Thread(target=process, daemon=True).start()

    def ai_color_correct(self):
        """Auto color correction using AI."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        self.update_status("Correcting colors...")

        try:
            result = self.image_enhancer.auto_color_correction(self.current_image)
            if result:
                self.current_image = result
                self.display_image()
                self.update_status("Colors corrected")
                self.add_to_history()
            else:
                messagebox.showerror("Error", "Failed to correct colors")
        except Exception as e:
            messagebox.showerror("Error", f"Color correction error: {str(e)}")

    # Filter and editing methods
    def apply_filter(self, filter_type):
        """Apply a filter to the current image."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        try:
            # Update image manager with current image
            self.image_manager.current_image = self.current_image
            if self.image_manager.apply_filter(filter_type):
                self.current_image = self.image_manager.get_current_image()
                self.display_image()
                self.update_status(f"Applied {filter_type} filter")
                self.add_to_history()
            else:
                messagebox.showerror("Error", f"Failed to apply {filter_type} filter")
        except Exception as e:
            messagebox.showerror("Error", f"Filter error: {str(e)}")

    def resize_image(self):
        """Resize the current image."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        current_width, current_height = self.current_image.size

        # Create resize dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Resize Image")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="New Width:").pack(pady=5)
        width_var = tk.IntVar(value=current_width)
        width_entry = ttk.Entry(dialog, textvariable=width_var)
        width_entry.pack(pady=5)

        ttk.Label(dialog, text="New Height:").pack(pady=5)
        height_var = tk.IntVar(value=current_height)
        height_entry = ttk.Entry(dialog, textvariable=height_var)
        height_entry.pack(pady=5)

        def apply_resize():
            try:
                new_width = width_var.get()
                new_height = height_var.get()

                if new_width > 0 and new_height > 0:
                    self.image_manager.current_image = self.current_image
                    if self.image_manager.resize_image(new_width, new_height):
                        self.current_image = self.image_manager.get_current_image()
                        self.display_image()
                        self.update_image_info()
                        self.update_status(f"Resized to {new_width}x{new_height}")
                        self.add_to_history()
                        dialog.destroy()
                    else:
                        messagebox.showerror("Error", "Failed to resize image")
                else:
                    messagebox.showerror("Error", "Invalid dimensions")
            except Exception as e:
                messagebox.showerror("Error", f"Resize error: {str(e)}")

        ttk.Button(dialog, text="Apply", command=apply_resize).pack(pady=10)

    def rotate_image(self):
        """Rotate the current image."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        angle = simpledialog.askfloat("Rotate Image",
                                    "Enter rotation angle (degrees):",
                                    initialvalue=90)
        if angle is None:
            return

        try:
            self.image_manager.current_image = self.current_image
            if self.image_manager.rotate_image(angle):
                self.current_image = self.image_manager.get_current_image()
                self.display_image()
                self.update_image_info()
                self.update_status(f"Rotated by {angle} degrees")
                self.add_to_history()
            else:
                messagebox.showerror("Error", "Failed to rotate image")
        except Exception as e:
            messagebox.showerror("Error", f"Rotation error: {str(e)}")

    def flip_image(self, direction):
        """Flip the current image."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        try:
            self.image_manager.current_image = self.current_image
            if self.image_manager.flip_image(direction):
                self.current_image = self.image_manager.get_current_image()
                self.display_image()
                self.update_status(f"Flipped {direction}")
                self.add_to_history()
            else:
                messagebox.showerror("Error", f"Failed to flip {direction}")
        except Exception as e:
            messagebox.showerror("Error", f"Flip error: {str(e)}")

    # Animation methods
    def add_frame(self):
        """Add current image as a new frame."""
        if not self.current_image:
            messagebox.showwarning("Warning", "No image loaded")
            return

        duration = simpledialog.askinteger("Frame Duration",
                                         "Enter frame duration (ms):",
                                         initialvalue=100, minvalue=10, maxvalue=5000)
        if duration is None:
            return

        try:
            self.gif_manager.add_frame(self.current_image, duration)
            self.update_animation_info()
            self.update_status("Frame added to animation")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add frame: {str(e)}")

    def remove_frame(self):
        """Remove current frame from animation."""
        if not self.gif_manager.frames:
            messagebox.showwarning("Warning", "No animation loaded")
            return

        try:
            current_index = self.gif_manager.current_frame_index
            if self.gif_manager.remove_frame(current_index):
                if self.gif_manager.frames:
                    self.current_image = self.gif_manager.get_frame()
                    self.display_image()
                self.update_animation_info()
                self.update_status("Frame removed from animation")
            else:
                messagebox.showerror("Error", "Failed to remove frame")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to remove frame: {str(e)}")

    def duplicate_frame(self):
        """Duplicate current frame in animation."""
        if not self.gif_manager.frames:
            messagebox.showwarning("Warning", "No animation loaded")
            return

        try:
            current_index = self.gif_manager.current_frame_index
            if self.gif_manager.duplicate_frame(current_index):
                self.update_animation_info()
                self.update_status("Frame duplicated")
            else:
                messagebox.showerror("Error", "Failed to duplicate frame")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to duplicate frame: {str(e)}")

    def next_frame(self):
        """Go to next frame in animation."""
        if self.gif_manager.frames:
            self.current_image = self.gif_manager.next_frame()
            if self.current_image:
                self.display_image()
                self.update_animation_info()

    def prev_frame(self):
        """Go to previous frame in animation."""
        if self.gif_manager.frames:
            self.current_image = self.gif_manager.previous_frame()
            if self.current_image:
                self.display_image()
                self.update_animation_info()

    def play_animation(self):
        """Play the animation."""
        if not self.gif_manager.frames or len(self.gif_manager.frames) < 2:
            messagebox.showwarning("Warning", "No animation to play")
            return

        self.animation_playing = True
        self.update_status("Playing animation...")
        self._animate()

    def stop_animation(self):
        """Stop the animation."""
        self.animation_playing = False
        self.update_status("Animation stopped")

    def _animate(self):
        """Internal animation loop."""
        if hasattr(self, 'animation_playing') and self.animation_playing:
            self.current_image = self.gif_manager.next_frame()
            if self.current_image:
                self.display_image()
                self.update_animation_info()

                # Schedule next frame
                current_duration = self.gif_manager.frame_durations[self.gif_manager.current_frame_index]
                self.root.after(current_duration, self._animate)

    # Zoom and view methods
    def zoom_in(self):
        """Zoom in on the image."""
        self.zoom_level = min(self.zoom_level * 1.2, 10.0)
        self.display_image()
        self.update_zoom_display()

    def zoom_out(self):
        """Zoom out on the image."""
        self.zoom_level = max(self.zoom_level / 1.2, 0.1)
        self.display_image()
        self.update_zoom_display()

    def zoom_fit(self):
        """Fit image to canvas."""
        if not self.current_image:
            return

        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        img_width, img_height = self.current_image.size

        if canvas_width > 1 and canvas_height > 1:
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            self.zoom_level = min(scale_x, scale_y) * 0.9  # 90% of fit
            self.display_image()
            self.update_zoom_display()

    def update_zoom_display(self):
        """Update the zoom level display."""
        self.zoom_label.config(text=f"Zoom: {int(self.zoom_level * 100)}%")

    # Adjustment methods
    def on_brightness_change(self, value):
        """Handle brightness adjustment."""
        if not self.current_image:
            return

        try:
            brightness_factor = 1.0 + (float(value) / 100.0)
            if hasattr(self, '_original_for_adjustments'):
                base_image = self._original_for_adjustments
            else:
                self._original_for_adjustments = self.current_image.copy()
                base_image = self._original_for_adjustments

            self.image_manager.current_image = base_image
            if self.image_manager.adjust_brightness(brightness_factor):
                self.current_image = self.image_manager.get_current_image()
                self.display_image()
        except Exception as e:
            self.logger.error(f"Brightness adjustment failed: {e}")

    def on_contrast_change(self, value):
        """Handle contrast adjustment."""
        if not self.current_image:
            return

        try:
            contrast_factor = 1.0 + (float(value) / 100.0)
            if hasattr(self, '_original_for_adjustments'):
                base_image = self._original_for_adjustments
            else:
                self._original_for_adjustments = self.current_image.copy()
                base_image = self._original_for_adjustments

            self.image_manager.current_image = base_image
            if self.image_manager.adjust_contrast(contrast_factor):
                self.current_image = self.image_manager.get_current_image()
                self.display_image()
        except Exception as e:
            self.logger.error(f"Contrast adjustment failed: {e}")

    # History methods
    def add_to_history(self):
        """Add current image to history."""
        if hasattr(self, '_original_for_adjustments'):
            delattr(self, '_original_for_adjustments')
        # Reset adjustment sliders
        self.brightness_var.set(0)
        self.contrast_var.set(0)

    def undo(self):
        """Undo last operation."""
        if self.image_manager.can_undo():
            self.image_manager.undo()
            self.current_image = self.image_manager.get_current_image()
            self.display_image()
            self.update_status("Undone")

    def redo(self):
        """Redo last operation."""
        if self.image_manager.can_redo():
            self.image_manager.redo()
            self.current_image = self.image_manager.get_current_image()
            self.display_image()
            self.update_status("Redone")

    # Event handlers
    def on_canvas_click(self, event):
        """Handle canvas click events."""
        pass

    def on_canvas_motion(self, event):
        """Handle canvas mouse motion."""
        if self.current_image:
            # Convert canvas coordinates to image coordinates
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)

            # Calculate image coordinates
            img_width, img_height = self.current_image.size
            display_width = int(img_width * self.zoom_level)
            display_height = int(img_height * self.zoom_level)

            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Calculate offset from center
            offset_x = canvas_x - canvas_width // 2
            offset_y = canvas_y - canvas_height // 2

            # Convert to image coordinates
            img_x = int((offset_x + display_width // 2) / self.zoom_level)
            img_y = int((offset_y + display_height // 2) / self.zoom_level)

            if 0 <= img_x < img_width and 0 <= img_y < img_height:
                self.coords_label.config(text=f"X: {img_x}, Y: {img_y}")
            else:
                self.coords_label.config(text="")

    def on_canvas_scroll(self, event):
        """Handle canvas scroll events for zooming."""
        if self.current_image:
            if event.delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()

    def update_status(self, message):
        """Update the status bar."""
        self.status_label.config(text=message)

    def on_closing(self):
        """Handle window closing."""
        if hasattr(self, 'animation_playing'):
            self.animation_playing = False

        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.logger.info("AI Image Editor closing")
            self.root.destroy()
