# AI Image Editor

A comprehensive, AI-powered image editor built in Python with advanced features including background removal, image enhancement, animation support, and real-time effects.

## Features

### 🤖 AI-Powered Tools
- **Background Removal**: Intelligent background removal using multiple AI models (rembg, GrabCut, color threshold)
- **Auto Enhancement**: Automatic image improvement with noise reduction, sharpening, and color correction
- **Image Upscaling**: AI-powered image upscaling with multiple interpolation methods
- **Denoising**: Advanced noise reduction using bilateral filtering, non-local means, and more
- **Color Correction**: Automatic white balance and color enhancement

### 🎨 Image Editing
- **Basic Operations**: Resize, rotate, flip, crop images
- **Filters**: Blur, sharpen, edge detection, emboss, and more
- **Adjustments**: Real-time brightness, contrast, and saturation controls
- **History**: Comprehensive undo/redo system

### 🎬 Animation Support
- **GIF Editing**: Load, edit, and save animated GIFs
- **Frame Management**: Add, remove, duplicate frames
- **Animation Timeline**: Frame-by-frame editing with duration controls
- **Playback**: Real-time animation preview

### 🖼️ Live Preview
- **Real-time Effects**: Live preview of adjustments and filters
- **Zoom Controls**: Zoom in/out, fit to window
- **Interactive Canvas**: Mouse coordinates and scroll-to-zoom

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Ai-image
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Dependencies

### Core Libraries
- **Pillow**: Image processing and manipulation
- **OpenCV**: Computer vision and advanced image processing
- **NumPy**: Numerical operations and array handling
- **tkinter**: GUI framework (included with Python)

### AI Libraries
- **rembg**: AI-powered background removal
- **torch & torchvision**: Deep learning framework for AI models
- **transformers**: Hugging Face transformers for AI models
- **diffusers**: Diffusion models for image generation

### Additional Libraries
- **scikit-image**: Advanced image processing algorithms
- **imageio**: Image and video I/O
- **moviepy**: Video processing for animation export
- **requests**: HTTP requests for AI APIs

## Usage

### Basic Operations

1. **Open an Image**: File → Open Image (Ctrl+O)
2. **Save Image**: File → Save (Ctrl+S) or Save As (Ctrl+Shift+S)
3. **Basic Editing**: Use Edit menu for resize, rotate, flip operations

### AI Features

1. **Remove Background**: AI Tools → Remove Background
2. **Enhance Image**: AI Tools → Auto Enhance
3. **Denoise**: AI Tools → Denoise Image
4. **Upscale**: AI Tools → Upscale Image

### Animation

1. **Open GIF**: File → Open GIF/Animation
2. **Create Animation**: File → Open GIF/Animation (select multiple images)
3. **Edit Frames**: Use Animation menu to add/remove/duplicate frames
4. **Export**: File → Export GIF

### Keyboard Shortcuts

- **Ctrl+O**: Open image
- **Ctrl+S**: Save image
- **Ctrl+Shift+S**: Save as
- **Ctrl+Z**: Undo
- **Ctrl+Y**: Redo
- **Ctrl++**: Zoom in
- **Ctrl+-**: Zoom out
- **Ctrl+0**: Fit to window

## Project Structure

```
Ai-image/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── src/
│   ├── gui/
│   │   └── ai_image_editor.py    # Main GUI application
│   ├── core/
│   │   └── image_manager.py      # Core image operations
│   ├── ai/
│   │   ├── background_remover.py # AI background removal
│   │   └── image_enhancer.py     # AI image enhancement
│   ├── animation/
│   │   └── gif_manager.py        # Animation and GIF handling
│   ├── filters/              # Image filters
│   └── utils/               # Utility functions
├── tests/                   # Unit tests
└── docs/                   # Documentation
```

## AI Models and Methods

### Background Removal
- **U²-Net**: Deep learning model for accurate background removal
- **GrabCut**: OpenCV's interactive foreground extraction
- **Color Threshold**: Simple color-based background removal
- **Edge Detection**: Contour-based background removal

### Image Enhancement
- **Bilateral Filtering**: Edge-preserving noise reduction
- **Non-local Means**: Advanced denoising algorithm
- **Unsharp Masking**: Professional sharpening technique
- **CLAHE**: Contrast Limited Adaptive Histogram Equalization
- **Auto Color Correction**: Gray world assumption for white balance

## Performance Tips

1. **Large Images**: Use lower zoom levels for better performance
2. **AI Operations**: AI features may take time depending on image size
3. **Memory Usage**: Close unused images to free memory
4. **Background Processing**: AI operations run in background threads

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **AI Model Loading**: First-time AI operations may download models
3. **Memory Issues**: Reduce image size or restart application
4. **Performance**: Close other applications for better performance

### Error Messages

- **"rembg not available"**: Install rembg library for AI background removal
- **"OpenCV not available"**: Install opencv-python for advanced features
- **"No image loaded"**: Open an image before applying operations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **rembg**: For AI-powered background removal
- **OpenCV**: For computer vision algorithms
- **Pillow**: For image processing capabilities
- **scikit-image**: For advanced image processing algorithms

## Future Enhancements

- Text overlay and drawing tools
- Layer support with blending modes
- Batch processing capabilities
- Plugin system for custom filters
- Cloud AI integration
- Video editing support
